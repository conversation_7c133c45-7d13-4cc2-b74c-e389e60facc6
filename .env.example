# Application URLs - For frontend and backend (required)
export FRONTEND_URL=http://localhost:3000
export BACKEND_URL=http://localhost:4000

# Secret key base
export SECRET_KEY_BASE=mHUYpR7Q1LtBo5eUCt/U6Vluj7gDARoMztdmRgyb8AyZcJN8EspidYIM3W5th8sW
# Generate this using -> `mix phx.gen.secret`

# Guardian - Authentication
export GUARDIAN_KEY=qKeHozNfIjr2Q8QvQYAm+HwoyDNz0t4yUsE8uEtEP6gp4pOnCtLXsvHDCACcdkE3
# Generate this using -> `mix guardian.gen.secret`

# Mailer reply
export WRAFT_HOSTNAME=example.com

# xelatex variables
export XELATEX_PATH=xelatex

# Application Mode
export MIX_ENV=dev

# deployement mode
export SELF_HOSTED=true

# Database - PostgreSQL
# Use for development environment
export DEV_DB_USERNAME=postgres
export DEV_DB_PASSWORD=postgres
export DEV_DB_NAME=wraft_doc_dev_temp
export DEV_DB_HOST=localhost
export DEV_DB_PORT=5432

# User for production environment
export DATABASE_URL=postgres://postgres:postgres@localhost:5432/wraft_doc_dev
#Example: postgres://<username>:<password>@<host>:<port>/<database>

# Phoenix - Application Server Configuration
export PHX_SERVER=true
export RELEASE_NAME=wraft_doc

# MinIO - Object Storage
export MINIO_URL=127.0.0.1:9000
export MINIO_HOST=127.0.0.1
export MINIO_PORT=9000
export MINIO_BUCKET=wraft
export MINIO_ROOT_USER=minioadmin
export MINIO_ROOT_PASSWORD=minioadmin

# SendGrid - Email Service
export SENDGRID_API_KEY=
# Get it from https://app.sendgrid.com/settings/api_keys

# Optional - Admin Credentials
export WRAFT_ADMIN_EMAIL=wraft_admin_email
export WRAFT_ADMIN_PASSWORD=wraft_admin_password

# PDF Tools - File Paths
export WKHTMLTOPDF_PATH=
export PDFTK_PATH=

# Sentry - Error Tracking
export SENTRY_DSN=https://<EMAIL>/1

# Typesense - Search Service
export TYPESENSE_API_KEY=xyz
export TYPESENSE_HOST=localhost
export TYPESENSE_PORT=8108
export TYPESENSE_SCHEME=http

# paddle - payment service
export PADDLE_API_KEY=
export PADDLE_WEBHOOK_SECRET_KEY=
# Paddle API Base URL:
# Use the appropriate URL based on the environment
# Production: https://api.paddle.com
# Development: https://sandbox-api.paddle.com
export PADDLE_BASE_URL=